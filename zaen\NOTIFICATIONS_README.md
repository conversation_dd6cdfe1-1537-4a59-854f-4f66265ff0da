# 🔔 نظام الإشعارات الذكية - Zain Smart Home

## نظرة عامة
تم إضافة نظام إشعارات ذكي متطور لتطبيق Zain Smart Home يوفر:
- **إشعارات الطقس الذكية** 🌤️
- **التوصيات المخصصة** 💡
- **تنبيهات الطقس المتطرف** ⚠️
- **اقتراحات أتمتة الأجهزة** 🏠

## الميزات الجديدة

### 1. خدمة الإشعارات الذكية (`NotificationService`)
- **تحليل بيانات الطقس**: يحلل البيانات الواردة من Home Assistant
- **إشعارات متدرجة**: 4 مستويات أولوية (منخفض، عادي، عالي، عاجل)
- **إشعارات مخصصة**: أيقونات وألوان مختلفة حسب نوع الإشعار
- **إعدادات قابلة للتخصيص**: يمكن تفعيل/إلغاء كل نوع إشعار

### 2. محرك التوصيات الذكية (`SmartRecommendations`)
يحلل 7 فئات من التوصيات:
- **المناخ** ❄️: تشغيل/إيقاف المكيف والتدفئة
- **جودة الهواء** 🌬️: فتح/إغلاق النوافذ والتهوية
- **الأمان** 🛡️: حماية من الأشعة والرياح القوية
- **توفير الطاقة** ⚡: استغلال الطاقة الشمسية
- **الراحة** 😌: تحسين الرطوبة والضغط
- **الحديقة** 🌱: توقيت الري المثالي
- **الإضاءة** 💡: تحسين الإضاءة حسب الطقس

### 3. واجهة إدارة الإشعارات (`NotificationsSettingsPage`)
- **إعدادات شاملة**: تحكم في جميع أنواع الإشعارات
- **الإشعارات النشطة**: عرض وإدارة الإشعارات الحالية
- **اختبار النظام**: إمكانية اختبار الإشعارات
- **واجهة عربية**: تصميم متجاوب ومناسب للغة العربية

## كيفية الاستخدام

### 1. التشغيل الأساسي
```bash
# في الراسبيري باي
cd myProject
python play.py

# في الهاتف
# فتح تطبيق Flutter
```

### 2. الوصول لإعدادات الإشعارات
- افتح التطبيق
- اذهب إلى الإعدادات
- اختر "إعدادات الإشعارات"

### 3. تخصيص الإشعارات
- **إشعارات الطقس**: تلقي تحديثات عند تغير الطقس
- **تنبيهات الطقس المتطرف**: تحذيرات للطقس الخطير
- **الاقتراحات الذكية**: توصيات مخصصة
- **اقتراحات الأتمتة**: توصيات لتشغيل الأجهزة

## الملفات المضافة/المحدثة

### ملفات جديدة:
- `zaen/lib/services/notification_service.dart` - خدمة الإشعارات الرئيسية
- `zaen/lib/services/smart_recommendations.dart` - محرك التوصيات الذكية
- `zaen/lib/services/service_locator.dart` - تهيئة الخدمات
- `zaen/lib/pages/notifications_settings_page.dart` - واجهة إدارة الإشعارات

### ملفات محدثة:
- `zaen/lib/modules/local/mqtt.dart` - ربط الإشعارات مع MQTT
- `zaen/lib/main.dart` - تهيئة الخدمات عند بدء التطبيق
- `zaen/pubspec.yaml` - إضافة مكتبة flutter_local_notifications
- `zaen/android/app/src/main/AndroidManifest.xml` - أذونات الإشعارات

## أنواع الإشعارات

### 1. إشعارات تغير الطقس 🌤️
- تغير درجة الحرارة بأكثر من 5 درجات
- تغير حالة الطقس (مشمس ← غائم)
- تغير الرطوبة بشكل كبير

### 2. تنبيهات الطقس المتطرف ⚠️
- درجة حرارة أعلى من 40°C أو أقل من 5°C
- رياح أقوى من 50 كم/ساعة
- رطوبة أعلى من 90%

### 3. التوصيات الذكية 💡
- **تشغيل المكيف**: عند الحر الشديد
- **فتح النوافذ**: في الطقس المعتدل
- **ري النباتات**: في الصباح الباكر
- **إطفاء الأضواء**: عند شروق الشمس

## التكامل مع النظام

### تدفق البيانات:
```
Home Assistant → MySQL → MQTT → Flutter App → NotificationService
                                                      ↓
                                            SmartRecommendations
                                                      ↓
                                              User Notifications
```

### معالجة الإشعارات:
1. **استلام بيانات الطقس** من MQTT
2. **تحليل البيانات** باستخدام محرك التوصيات
3. **إنشاء إشعارات مخصصة** حسب الحالة
4. **عرض الإشعارات** للمستخدم
5. **تنفيذ الإجراءات** عند النقر

## الإعدادات المتقدمة

### تخصيص التوصيات:
- **مستوى الحساسية**: تحديد حد التغيرات المطلوبة للإشعار
- **التوقيت**: تحديد أوقات عرض الإشعارات
- **الأولوية**: ترتيب أهمية أنواع الإشعارات المختلفة

### إدارة الطاقة:
- **توفير البطارية**: تقليل تكرار الإشعارات
- **الوضع الصامت**: إيقاف الإشعارات في أوقات محددة
- **الإشعارات الذكية**: عرض الإشعارات المهمة فقط

## استكشاف الأخطاء

### مشاكل شائعة:
1. **عدم ظهور الإشعارات**: تأكد من أذونات الإشعارات في الإعدادات
2. **عدم دقة التوصيات**: تحقق من اتصال MQTT وبيانات الطقس
3. **استهلاك البطارية**: قلل تكرار التحديثات في الإعدادات

### سجلات الأخطاء:
- تحقق من console logs في Flutter
- راجع سجلات MQTT في الراسبيري باي
- تأكد من اتصال قاعدة البيانات

## التطوير المستقبلي

### ميزات مخططة:
- **تعلم آلي**: تحسين التوصيات بناءً على سلوك المستخدم
- **إشعارات صوتية**: دعم المساعدات الصوتية
- **تكامل أوسع**: ربط مع المزيد من أجهزة IoT
- **تحليلات متقدمة**: إحصائيات استخدام الطاقة

---

## 📞 الدعم الفني
للمساعدة أو الاستفسارات، يرجى مراجعة الكود أو إنشاء issue في المشروع.

**تم تطوير النظام بواسطة Augment Agent** 🤖
