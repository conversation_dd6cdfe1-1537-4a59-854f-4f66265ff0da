# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 18ms
    create-X86-model 16ms
    create-X86_64-model 35ms
    create-module-model 21ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 14ms
    create-module-model 15ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 28ms
    create-ARM64_V8A-model 15ms
    create-X86-model 19ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 338ms
create_cxx_tasks completed in 347ms

