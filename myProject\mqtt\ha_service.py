#!/usr/bin/env python3
"""
خدمة التكامل مع Home Assistant
تدمج بيانات Home Assistant مع نظام البيت الذكي
"""

import sys
import os
import time
import json
import threading
from datetime import datetime, timedelta

# إضافة المسارات المطلوبة
sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/mqtt')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>')

try:
    from ha import HomeAssistantIntegration
    import static as st
    from database_helper import DatabaseHelper
    import paho.mqtt.client as mqtt
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class HAService:
    """خدمة التكامل مع Home Assistant"""
    
    def __init__(self):
        self.ha_integration = HomeAssistantIntegration()
        self.db_helper = DatabaseHelper()
        self.mqtt_client = None
        self.running = False
        
        # إعدادات MQTT
        self.mqtt_host = st.ip if hasattr(st, 'ip') else 'zain.local'
        self.mqtt_port = 1883
        
        # مواضيع MQTT
        self.weather_topic = "homeassistant/weather"
        self.sensor_topic = "homeassistant/sensor"
        self.automation_topic = "homeassistant/automation"
        
        self.setup_mqtt()
    
    def setup_mqtt(self):
        """إعداد اتصال MQTT"""
        try:
            self.mqtt_client = mqtt.Client(client_id="ha_service")
            self.mqtt_client.on_connect = self.on_mqtt_connect
            self.mqtt_client.on_message = self.on_mqtt_message
            self.mqtt_client.on_disconnect = self.on_mqtt_disconnect
            
            self.mqtt_client.connect(self.mqtt_host, self.mqtt_port, 60)
            self.mqtt_client.loop_start()
            
        except Exception as e:
            print(f"خطأ في إعداد MQTT: {e}")
    
    def on_mqtt_connect(self, client, userdata, flags, rc):
        """عند الاتصال بـ MQTT"""
        if rc == 0:
            print("✅ تم الاتصال بـ MQTT بنجاح")
            # الاشتراك في المواضيع المطلوبة
            client.subscribe("phone/weather/request")
            client.subscribe("phone/sensor/request")
            client.subscribe("automation/trigger")
        else:
            print(f"❌ فشل الاتصال بـ MQTT: {rc}")
    
    def on_mqtt_disconnect(self, client, userdata, rc):
        """عند قطع الاتصال مع MQTT"""
        print(f"⚠️ تم قطع الاتصال مع MQTT: {rc}")
    
    def on_mqtt_message(self, client, userdata, msg, properties=None):
        """معالجة رسائل MQTT"""
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            
            if topic == "phone/weather/request":
                self.handle_weather_request(payload)
            elif topic == "phone/sensor/request":
                self.handle_sensor_request(payload)
            elif topic == "automation/trigger":
                self.handle_automation_trigger(payload)
                
        except Exception as e:
            print(f"خطأ في معالجة رسالة MQTT: {e}")
    
    def handle_weather_request(self, payload):
        """معالجة طلب بيانات الطقس"""
        try:
            print(f"📡 تم استلام طلب بيانات الطقس: {payload}")

            # جلب بيانات الطقس من قاعدة البيانات
            weather_data = self.ha_integration.get_weather_data()

            if weather_data:
                # إرسال البيانات عبر MQTT
                response = {
                    'status': 'success',
                    'data': {
                        'entity_id': weather_data.get('entity_id'),
                        'temperature': weather_data.get('temperature'),
                        'humidity': weather_data.get('humidity'),
                        'condition': weather_data.get('weather_condition'),
                        'friendly_name': weather_data.get('friendly_name'),
                        'pressure': weather_data.get('pressure'),
                        'wind_speed': weather_data.get('wind_speed'),
                        'wind_bearing': weather_data.get('wind_bearing'),
                        'cloud_coverage': weather_data.get('cloud_coverage'),
                        'uv_index': weather_data.get('uv_index'),
                        'dew_point': weather_data.get('dew_point'),
                        'temperature_unit': weather_data.get('temperature_unit'),
                        'pressure_unit': weather_data.get('pressure_unit'),
                        'wind_speed_unit': weather_data.get('wind_speed_unit'),
                        'attribution': weather_data.get('attribution'),
                        'last_updated': str(weather_data.get('last_updated'))
                    }
                }

                print(f"✅ تم جلب بيانات الطقس: {weather_data.get('temperature')}°C")

                # إرسال البيانات إلى موضوعات متعددة للتأكد من الوصول
                topics = [
                    "homeassistant/weather/response",
                    "weather/data",
                    self.weather_topic + "/response"
                ]

                for topic in topics:
                    self.mqtt_client.publish(
                        topic,
                        json.dumps(response, ensure_ascii=False)
                    )
                    print(f"📤 تم إرسال بيانات الطقس إلى: {topic}")

            else:
                # إرسال رسالة خطأ
                error_response = {
                    'status': 'error',
                    'message': 'لا توجد بيانات طقس متاحة في قاعدة البيانات'
                }

                print("❌ لم يتم العثور على بيانات طقس في قاعدة البيانات")

                topics = [
                    "homeassistant/weather/response",
                    "weather/data",
                    self.weather_topic + "/response"
                ]

                for topic in topics:
                    self.mqtt_client.publish(
                        topic,
                        json.dumps(error_response, ensure_ascii=False)
                    )
                
        except Exception as e:
            print(f"خطأ في معالجة طلب الطقس: {e}")
    
    def handle_sensor_request(self, payload):
        """معالجة طلب بيانات المستشعرات"""
        try:
            request_data = json.loads(payload) if payload else {}
            sensor_type = request_data.get('type')
            location = request_data.get('location')
            
            sensor_data = self.ha_integration.get_sensor_data(sensor_type, location)
            
            response = {
                'status': 'success',
                'data': sensor_data
            }
            
            self.mqtt_client.publish(
                self.sensor_topic + "/response",
                json.dumps(response, ensure_ascii=False)
            )
            
        except Exception as e:
            print(f"خطأ في معالجة طلب المستشعرات: {e}")
    
    def handle_automation_trigger(self, payload):
        """معالجة تشغيل الأتمتة"""
        try:
            trigger_data = json.loads(payload)
            trigger_type = trigger_data.get('type')
            
            if trigger_type == 'weather_based':
                self.process_weather_automation(trigger_data)
            elif trigger_type == 'sensor_based':
                self.process_sensor_automation(trigger_data)
                
        except Exception as e:
            print(f"خطأ في معالجة تشغيل الأتمتة: {e}")
    
    def process_weather_automation(self, trigger_data):
        """معالجة أتمتة مبنية على الطقس"""
        try:
            weather_data = self.ha_integration.get_weather_data()
            
            if not weather_data:
                return
            
            conditions = trigger_data.get('conditions', {})
            actions = trigger_data.get('actions', [])
            
            # فحص الشروط
            conditions_met = True
            
            if 'temperature_min' in conditions:
                if weather_data.get('temperature', 0) < conditions['temperature_min']:
                    conditions_met = False
            
            if 'temperature_max' in conditions:
                if weather_data.get('temperature', 100) > conditions['temperature_max']:
                    conditions_met = False
            
            if 'humidity_min' in conditions:
                if weather_data.get('humidity', 0) < conditions['humidity_min']:
                    conditions_met = False
            
            if 'weather_condition' in conditions:
                if weather_data.get('weather_condition') != conditions['weather_condition']:
                    conditions_met = False
            
            # تنفيذ الإجراءات إذا تحققت الشروط
            if conditions_met:
                for action in actions:
                    self.execute_automation_action(action)
                    
        except Exception as e:
            print(f"خطأ في معالجة أتمتة الطقس: {e}")
    
    def execute_automation_action(self, action):
        """تنفيذ إجراء الأتمتة"""
        try:
            action_type = action.get('type')
            
            if action_type == 'device_control':
                # التحكم في الأجهزة
                device_id = action.get('device_id')
                command = action.get('command')
                
                # إرسال أمر التحكم عبر MQTT
                control_topic = f"ROOMS/{action.get('room', '*')}/{action.get('device_type', '*')}/{device_id}"
                self.mqtt_client.publish(control_topic, command)
                
            elif action_type == 'notification':
                # إرسال إشعار
                message = action.get('message')
                self.mqtt_client.publish("notifications/send", message)
                
        except Exception as e:
            print(f"خطأ في تنفيذ إجراء الأتمتة: {e}")
    
    def start_service(self):
        """بدء الخدمة"""
        print("🚀 بدء خدمة التكامل مع Home Assistant...")

        # بدء التحديث المستمر للكيانات
        self.ha_integration.start_continuous_update(interval=300)  # كل 5 دقائق

        # إرسال بيانات الطقس الأولية
        self.send_initial_weather_data()

        # بدء إرسال بيانات الطقس بشكل دوري
        self.start_periodic_weather_updates()

        self.running = True

        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️ إيقاف الخدمة...")
            self.stop_service()

    def send_initial_weather_data(self):
        """إرسال بيانات الطقس الأولية"""
        try:
            print("📡 إرسال بيانات الطقس الأولية...")
            self.handle_weather_request("")
        except Exception as e:
            print(f"خطأ في إرسال بيانات الطقس الأولية: {e}")

    def start_periodic_weather_updates(self):
        """بدء التحديث الدوري لبيانات الطقس"""
        def periodic_update():
            while self.running:
                try:
                    time.sleep(300)  # كل 5 دقائق
                    if self.running:
                        print("🔄 تحديث دوري لبيانات الطقس...")
                        self.handle_weather_request("")
                except Exception as e:
                    print(f"خطأ في التحديث الدوري للطقس: {e}")

        # تشغيل التحديث الدوري في thread منفصل
        weather_thread = threading.Thread(target=periodic_update, daemon=True)
        weather_thread.start()
        print("✅ تم بدء التحديث الدوري لبيانات الطقس")
    
    def stop_service(self):
        """إيقاف الخدمة"""
        self.running = False
        
        # إيقاف التحديث المستمر
        self.ha_integration.stop_continuous_update()
        
        # إغلاق اتصال MQTT
        if self.mqtt_client:
            self.mqtt_client.loop_stop()
            self.mqtt_client.disconnect()
        
        print("✅ تم إيقاف الخدمة بنجاح")

def main():
    """الدالة الرئيسية"""
    service = HAService()
    service.start_service()

if __name__ == "__main__":
    main()
