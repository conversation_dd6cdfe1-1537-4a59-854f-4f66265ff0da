# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 75ms]
    create-module-model 10ms
    [gap of 33ms]
  create-initial-cxx-model completed in 118ms
create_cxx_tasks completed in 123ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 87ms
create_cxx_tasks completed in 92ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 93ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 26ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 148ms
create_cxx_tasks completed in 153ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 90ms
create_cxx_tasks completed in 95ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 97ms
create_cxx_tasks completed in 102ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 10ms]
    create-ARMEABI_V7A-model 10ms
    [gap of 80ms]
  create-initial-cxx-model completed in 100ms
create_cxx_tasks completed in 105ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 96ms
create_cxx_tasks completed in 101ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 111ms
create_cxx_tasks completed in 116ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 114ms
create_cxx_tasks completed in 120ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 104ms
create_cxx_tasks completed in 108ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 99ms
create_cxx_tasks completed in 104ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 90ms
create_cxx_tasks completed in 95ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 93ms
create_cxx_tasks completed in 96ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 17ms
    create-X86-model 18ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 14ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 16ms
    create-X86-model 12ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 256ms
create_cxx_tasks completed in 266ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 122ms]
    create-X86-model 20ms
  create-initial-cxx-model completed in 151ms
create_cxx_tasks completed in 156ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 91ms
create_cxx_tasks completed in 95ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-ARMEABI_V7A-model 16ms
    create-X86-model 16ms
    create-X86_64-model 15ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 16ms
    create-X86_64-model 15ms
    create-module-model
      create-project-model 16ms
    create-module-model completed in 16ms
    create-ARM64_V8A-model 15ms
  create-initial-cxx-model completed in 137ms
  [gap of 16ms]
create_cxx_tasks completed in 153ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 23ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 19ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 235ms
create_cxx_tasks completed in 244ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 12ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 51ms
    create-X86_64-model 11ms
    [gap of 18ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
  create-initial-cxx-model completed in 227ms
create_cxx_tasks completed in 233ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    create-ARMEABI_V7A-model 10ms
    [gap of 19ms]
    create-X86_64-model 11ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    [gap of 31ms]
    create-X86-model 10ms
  create-initial-cxx-model completed in 171ms
create_cxx_tasks completed in 178ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 19ms
    [gap of 19ms]
    create-ARM64_V8A-model 11ms
    create-X86_64-model 10ms
    [gap of 31ms]
    create-X86-model 15ms
    create-X86_64-model 15ms
    create-module-model 14ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    [gap of 10ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 199ms
create_cxx_tasks completed in 204ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 124ms
    create-X86_64-model 29ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 12ms
    [gap of 10ms]
    create-X86-model 11ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 341ms
create_cxx_tasks completed in 347ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 20ms
    [gap of 11ms]
    create-ARMEABI_V7A-model 28ms
    create-ARM64_V8A-model 21ms
    create-X86-model 18ms
    create-X86_64-model 18ms
    create-module-model 13ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 22ms
    create-X86-model 29ms
    create-X86_64-model 32ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 27ms
    create-ARM64_V8A-model 16ms
    create-X86-model 22ms
    create-X86_64-model 22ms
  create-initial-cxx-model completed in 361ms
  [gap of 11ms]
create_cxx_tasks completed in 373ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 17ms
    create-X86-model 16ms
    create-X86_64-model 21ms
    create-module-model 30ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 10ms
    create-X86-model 12ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 257ms
  [gap of 10ms]
create_cxx_tasks completed in 267ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 21ms
    [gap of 11ms]
    create-ARMEABI_V7A-model 22ms
    create-ARM64_V8A-model 103ms
    create-X86-model 248ms
    create-X86_64-model 70ms
    create-module-model
      [gap of 55ms]
      create-ndk-meta-abi-list 14ms
      [gap of 14ms]
    create-module-model completed in 83ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 44ms
    create-ARM64_V8A-model 18ms
    create-X86-model 39ms
    create-X86_64-model 92ms
    create-module-model
      [gap of 24ms]
      create-ndk-meta-abi-list 15ms
    create-module-model completed in 43ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 32ms
    create-ARM64_V8A-model 33ms
    create-X86-model 19ms
    create-X86_64-model 33ms
  create-initial-cxx-model completed in 958ms
  [gap of 19ms]
create_cxx_tasks completed in 977ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 87ms]
    create-module-model 11ms
    [gap of 14ms]
    create-ARM64_V8A-model 21ms
    create-X86-model 13ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 158ms
create_cxx_tasks completed in 163ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    [gap of 14ms]
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    [gap of 22ms]
    create-ARMEABI_V7A-model 11ms
    [gap of 18ms]
    create-X86_64-model 10ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 10ms
    create-X86-model 10ms
  create-initial-cxx-model completed in 160ms
create_cxx_tasks completed in 165ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 13ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 13ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 15ms
    create-X86_64-model 13ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 13ms
    [gap of 10ms]
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 212ms
create_cxx_tasks completed in 219ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 16ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 11ms
    create-X86_64-model 17ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    create-X86-model 47ms
    create-X86_64-model 14ms
    create-module-model 11ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 19ms
    create-X86-model 12ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 288ms
create_cxx_tasks completed in 295ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 18ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    [gap of 10ms]
    create-X86-model 13ms
    create-X86_64-model 12ms
    create-module-model 16ms
    [gap of 20ms]
    create-ARM64_V8A-model 16ms
    create-X86-model 10ms
    create-X86_64-model 11ms
    [gap of 42ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 220ms
create_cxx_tasks completed in 225ms

