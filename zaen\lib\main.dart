import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/themes/theme_manager.dart';
import 'package:zaen/shared/settings/settings.dart';
import 'package:zaen/view/home/<USER>';
import 'package:zaen/view/register/reg.dart';
import 'package:zaen/view/register/regscreen.dart';
import 'package:zaen/view/room/room.dart';
import 'package:zaen/view/home/<USER>';
import 'package:zaen/services/service_locator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة جميع الخدمات
  await ServiceLocator.init();

  // تهيئة كونترولر الإعدادات
  Get.put(SettingsController());

  // تعيين نمط شفاف للشريط السفلي للنظام
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.light,
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarContrastEnforced: false,
      systemStatusBarContrastEnforced: false));

  // تفعيل وضع edge-to-edge
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  runApp(const MyApp());
}

// Stateless
// Stateful

// class MyApp

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Zaen Smart Home',
      theme: ThemeManager.lightTheme,
      darkTheme: ThemeManager.darkTheme,
      themeMode: ThemeMode.system,
      home: const wait(),

      // routes: {'room': (context) => Room()},
      onGenerateRoute: (RouteSettings settings) {
        print('build route for ${settings.name}');
        Map getPages = <String, GetPageRoute<dynamic>>{
          'room': GetPageRoute(
            routeName: "room",
            page: () => const Room(),
          ),
          'home': GetPageRoute(
            routeName: "home",
            page: () => Home(),
          ),
          'reg': GetPageRoute(
            routeName: "reg",
            page: () => Scanner(),
          ),
          'wait': GetPageRoute(
            routeName: "wait",
            page: () => wait(),
          )
        };
        GetPageRoute? Route = getPages[settings.name];

        return Route;
        // var routes = <String, WidgetBuilder>{
        //   "room": (ctx) => Room(
        //         settings.arguments,
        //       ),
        // };
        // WidgetBuilder? builder = routes[settings.name];
        // return MaterialPageRoute(builder: (ctx) => builder!(ctx)
        // );
      },
    );
  }
}
