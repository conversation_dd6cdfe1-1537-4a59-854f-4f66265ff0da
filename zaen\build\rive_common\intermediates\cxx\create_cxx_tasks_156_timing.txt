# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    [gap of 10ms]
    create-variant-model 10ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 12ms
    create-module-model 10ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 14ms
    create-X86-model 11ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 223ms
create_cxx_tasks completed in 233ms

