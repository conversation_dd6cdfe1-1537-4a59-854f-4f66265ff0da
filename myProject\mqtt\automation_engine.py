#!/usr/bin/env python3
"""
محرك الأتمتة الذكي
يستخدم بيانات الطقس والكيانات لتنفيذ أتمتة ذكية
"""

import sys
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# إضافة المسارات المطلوبة
sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/mqtt')
sys.path.append('/home/<USER>')

try:
    from ha import HomeAssistantIntegration
    import static as st
    from database_helper import DatabaseHelper
    import paho.mqtt.client as mqtt
    import mysql.connector
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class AutomationEngine:
    """محرك الأتمتة الذكي"""
    
    def __init__(self):
        self.ha_integration = HomeAssistantIntegration()
        self.db_helper = DatabaseHelper()
        self.mqtt_client = None
        self.running = False
        
        # قواعد الأتمتة المحملة
        self.automation_rules = []
        self.active_automations = {}
        
        # إعدادات MQTT
        self.mqtt_host = st.ip if hasattr(st, 'ip') else 'zain.local'
        self.mqtt_port = 1883
        
        self.setup_mqtt()
        self.load_automation_rules()
        self.create_automation_tables()
    
    def setup_mqtt(self):
        """إعداد اتصال MQTT"""
        try:
            self.mqtt_client = mqtt.Client(client_id="automation_engine")
            self.mqtt_client.on_connect = self.on_mqtt_connect
            self.mqtt_client.on_message = self.on_mqtt_message
            
            self.mqtt_client.connect(self.mqtt_host, self.mqtt_port, 60)
            self.mqtt_client.loop_start()
            
        except Exception as e:
            print(f"خطأ في إعداد MQTT: {e}")
    
    def on_mqtt_connect(self, client, userdata, flags, rc):
        """عند الاتصال بـ MQTT"""
        if rc == 0:
            print("✅ محرك الأتمتة متصل بـ MQTT")
            # الاشتراك في المواضيع المطلوبة
            client.subscribe("automation/create")
            client.subscribe("automation/delete")
            client.subscribe("automation/enable")
            client.subscribe("automation/disable")
            client.subscribe("homeassistant/+/+")  # جميع كيانات Home Assistant
        else:
            print(f"❌ فشل اتصال محرك الأتمتة بـ MQTT: {rc}")
    
    def on_mqtt_message(self, client, userdata, msg, properties=None):
        """معالجة رسائل MQTT"""
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            
            if topic == "automation/create":
                self.create_automation_rule(json.loads(payload))
            elif topic == "automation/delete":
                self.delete_automation_rule(json.loads(payload))
            elif topic == "automation/enable":
                self.enable_automation_rule(json.loads(payload))
            elif topic == "automation/disable":
                self.disable_automation_rule(json.loads(payload))
            elif topic.startswith("homeassistant/"):
                self.process_entity_update(topic, payload)
                
        except Exception as e:
            print(f"خطأ في معالجة رسالة MQTT: {e}")
    
    def create_automation_tables(self):
        """إنشاء جداول الأتمتة"""
        connection = None
        cursor = None
        
        try:
            connection = self.db_helper.get_connection()
            cursor = connection.cursor()
            
            # جدول قواعد الأتمتة
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS automation_rules (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                conditions JSON NOT NULL,
                actions JSON NOT NULL,
                enabled BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_triggered TIMESTAMP NULL,
                trigger_count INT DEFAULT 0,
                INDEX idx_enabled (enabled),
                INDEX idx_name (name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            
            # جدول سجل تنفيذ الأتمتة
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS automation_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                rule_id INT,
                triggered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                conditions_met JSON,
                actions_executed JSON,
                success BOOLEAN DEFAULT TRUE,
                error_message TEXT,
                execution_time_ms INT,
                FOREIGN KEY (rule_id) REFERENCES automation_rules(id) ON DELETE CASCADE,
                INDEX idx_triggered_at (triggered_at),
                INDEX idx_rule_id (rule_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            
            connection.commit()
            print("✅ تم إنشاء جداول الأتمتة بنجاح")
            
        except mysql.connector.Error as e:
            print(f"❌ خطأ في إنشاء جداول الأتمتة: {e}")
            if connection:
                connection.rollback()
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    def load_automation_rules(self):
        """تحميل قواعد الأتمتة من قاعدة البيانات"""
        try:
            query = "SELECT * FROM automation_rules WHERE enabled = TRUE"
            result = self.db_helper.execute_query(query, fetch='all')
            
            if result:
                self.automation_rules = []
                for row in result:
                    rule = {
                        'id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'conditions': json.loads(row[3]),
                        'actions': json.loads(row[4]),
                        'enabled': row[5],
                        'last_triggered': row[7],
                        'trigger_count': row[8]
                    }
                    self.automation_rules.append(rule)
                
                print(f"✅ تم تحميل {len(self.automation_rules)} قاعدة أتمتة")
            
        except Exception as e:
            print(f"❌ خطأ في تحميل قواعد الأتمتة: {e}")
    
    def create_automation_rule(self, rule_data):
        """إنشاء قاعدة أتمتة جديدة"""
        try:
            query = """
            INSERT INTO automation_rules (name, description, conditions, actions, enabled)
            VALUES (%s, %s, %s, %s, %s)
            """
            
            params = (
                rule_data['name'],
                rule_data.get('description', ''),
                json.dumps(rule_data['conditions']),
                json.dumps(rule_data['actions']),
                rule_data.get('enabled', True)
            )
            
            self.db_helper.execute_query(query, params)
            self.load_automation_rules()  # إعادة تحميل القواعد
            
            print(f"✅ تم إنشاء قاعدة أتمتة جديدة: {rule_data['name']}")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة الأتمتة: {e}")
    
    def process_entity_update(self, topic, payload):
        """معالجة تحديث الكيانات وفحص قواعد الأتمتة"""
        try:
            # استخراج معلومات الكيان من الموضوع
            topic_parts = topic.split('/')
            if len(topic_parts) >= 3:
                entity_type = topic_parts[1]
                entity_id = topic_parts[2]
                
                # فحص جميع قواعد الأتمتة
                for rule in self.automation_rules:
                    if self.check_rule_conditions(rule, entity_type, entity_id, payload):
                        self.execute_rule_actions(rule)
                        
        except Exception as e:
            print(f"خطأ في معالجة تحديث الكيان: {e}")
    
    def check_rule_conditions(self, rule, entity_type, entity_id, payload):
        """فحص شروط قاعدة الأتمتة"""
        try:
            conditions = rule['conditions']
            
            # فحص شروط الطقس
            if 'weather' in conditions:
                weather_data = self.ha_integration.get_weather_data()
                if not self.check_weather_conditions(conditions['weather'], weather_data):
                    return False
            
            # فحص شروط الوقت
            if 'time' in conditions:
                if not self.check_time_conditions(conditions['time']):
                    return False
            
            # فحص شروط الكيانات
            if 'entities' in conditions:
                if not self.check_entity_conditions(conditions['entities'], entity_type, entity_id, payload):
                    return False
            
            # فحص شروط الأجهزة
            if 'devices' in conditions:
                if not self.check_device_conditions(conditions['devices']):
                    return False
            
            return True
            
        except Exception as e:
            print(f"خطأ في فحص شروط القاعدة: {e}")
            return False
    
    def check_weather_conditions(self, weather_conditions, weather_data):
        """فحص شروط الطقس"""
        if not weather_data:
            return False
        
        for condition, value in weather_conditions.items():
            if condition == 'temperature_min' and weather_data.get('temperature', 0) < value:
                return False
            elif condition == 'temperature_max' and weather_data.get('temperature', 100) > value:
                return False
            elif condition == 'humidity_min' and weather_data.get('humidity', 0) < value:
                return False
            elif condition == 'humidity_max' and weather_data.get('humidity', 100) > value:
                return False
            elif condition == 'condition' and weather_data.get('weather_condition') != value:
                return False
        
        return True
    
    def check_time_conditions(self, time_conditions):
        """فحص شروط الوقت"""
        now = datetime.now()
        
        for condition, value in time_conditions.items():
            if condition == 'hour_min' and now.hour < value:
                return False
            elif condition == 'hour_max' and now.hour > value:
                return False
            elif condition == 'weekday' and now.weekday() not in value:
                return False
            elif condition == 'date_range':
                start_date = datetime.strptime(value['start'], '%Y-%m-%d').date()
                end_date = datetime.strptime(value['end'], '%Y-%m-%d').date()
                if not (start_date <= now.date() <= end_date):
                    return False
        
        return True
    
    def check_entity_conditions(self, entity_conditions, entity_type, entity_id, payload):
        """فحص شروط الكيانات"""
        for condition in entity_conditions:
            if condition.get('type') == entity_type and condition.get('id') == entity_id:
                expected_state = condition.get('state')
                if expected_state and payload != expected_state:
                    return False
        
        return True
    
    def check_device_conditions(self, device_conditions):
        """فحص شروط الأجهزة"""
        for condition in device_conditions:
            device_id = condition.get('device_id')
            expected_state = condition.get('state')
            
            # جلب حالة الجهاز من قاعدة البيانات
            device_info = self.db_helper.get_device_info(device_id)
            if device_info and device_info[4] != expected_state:  # العمود الخامس هو الحالة
                return False
        
        return True
    
    def execute_rule_actions(self, rule):
        """تنفيذ إجراءات قاعدة الأتمتة"""
        start_time = time.time()
        executed_actions = []
        success = True
        error_message = None
        
        try:
            actions = rule['actions']
            
            for action in actions:
                action_type = action.get('type')
                
                if action_type == 'device_control':
                    self.execute_device_control(action)
                elif action_type == 'notification':
                    self.execute_notification(action)
                elif action_type == 'scene':
                    self.execute_scene(action)
                elif action_type == 'delay':
                    time.sleep(action.get('seconds', 1))
                
                executed_actions.append(action)
            
            # تحديث إحصائيات القاعدة
            self.update_rule_stats(rule['id'])
            
        except Exception as e:
            success = False
            error_message = str(e)
            print(f"❌ خطأ في تنفيذ إجراءات القاعدة {rule['name']}: {e}")
        
        # تسجيل التنفيذ
        execution_time = int((time.time() - start_time) * 1000)
        self.log_automation_execution(
            rule['id'], 
            rule['conditions'], 
            executed_actions, 
            success, 
            error_message, 
            execution_time
        )
        
        if success:
            print(f"✅ تم تنفيذ قاعدة الأتمتة: {rule['name']}")
    
    def execute_device_control(self, action):
        """تنفيذ التحكم في الأجهزة"""
        device_id = action.get('device_id')
        command = action.get('command')
        room = action.get('room', '*')
        device_type = action.get('device_type', '*')
        
        # إرسال أمر التحكم عبر MQTT
        control_topic = f"ROOMS/{room}/{device_type}/{device_id}"
        self.mqtt_client.publish(control_topic, command)
    
    def execute_notification(self, action):
        """تنفيذ إرسال الإشعارات"""
        message = action.get('message')
        notification_type = action.get('notification_type', 'info')
        
        notification_data = {
            'message': message,
            'type': notification_type,
            'timestamp': datetime.now().isoformat()
        }
        
        self.mqtt_client.publish("notifications/send", json.dumps(notification_data))
    
    def execute_scene(self, action):
        """تنفيذ مشهد"""
        scene_name = action.get('scene_name')
        scene_actions = action.get('actions', [])
        
        for scene_action in scene_actions:
            if scene_action.get('type') == 'device_control':
                self.execute_device_control(scene_action)
    
    def update_rule_stats(self, rule_id):
        """تحديث إحصائيات القاعدة"""
        query = """
        UPDATE automation_rules 
        SET last_triggered = NOW(), trigger_count = trigger_count + 1 
        WHERE id = %s
        """
        self.db_helper.execute_query(query, (rule_id,))
    
    def log_automation_execution(self, rule_id, conditions, actions, success, error_message, execution_time):
        """تسجيل تنفيذ الأتمتة"""
        query = """
        INSERT INTO automation_log 
        (rule_id, conditions_met, actions_executed, success, error_message, execution_time_ms)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        params = (
            rule_id,
            json.dumps(conditions),
            json.dumps(actions),
            success,
            error_message,
            execution_time
        )
        
        self.db_helper.execute_query(query, params)
    
    def start_engine(self):
        """بدء محرك الأتمتة"""
        print("🚀 بدء محرك الأتمتة الذكي...")
        self.running = True
        
        # بدء التحديث الدوري لقواعد الأتمتة
        def periodic_check():
            while self.running:
                try:
                    # إعادة تحميل القواعد كل 5 دقائق
                    self.load_automation_rules()
                    time.sleep(300)
                except Exception as e:
                    print(f"خطأ في الفحص الدوري: {e}")
                    time.sleep(60)
        
        check_thread = threading.Thread(target=periodic_check, daemon=True)
        check_thread.start()
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️ إيقاف محرك الأتمتة...")
            self.stop_engine()
    
    def stop_engine(self):
        """إيقاف محرك الأتمتة"""
        self.running = False
        
        if self.mqtt_client:
            self.mqtt_client.loop_stop()
            self.mqtt_client.disconnect()
        
        print("✅ تم إيقاف محرك الأتمتة بنجاح")

def main():
    """الدالة الرئيسية"""
    engine = AutomationEngine()
    engine.start_engine()

if __name__ == "__main__":
    main()
